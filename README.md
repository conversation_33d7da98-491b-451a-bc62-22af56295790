# Adeptos Go

## Architecture
```
  adeptos_go/
│
├── cmd/                    
│   └── main.go             
│
├── internal/
│   ├── adapters/             
│   │   ├── http/           
│   │   │   ├── router.go
│   │   │   ├── handlers/
│   │   │   │   └── lead_handler.go
│   │   │   └── response/
│   │   │       └── response.go
│   │   │
│   │   └── openai/
│   │
│   ├── service/          
│   │   └── lead_service.go
│   │
│   ├── repo/           
│   │   └── lead_repo.go
│   │
│   ├── domain/
│   │   └── lead.go
│   │
│   └── config/
│       ├── container.go
│       ├── db.go
│       └── env.go
│
├── go.mod
├── go.sum
└── .env        
```

## Create database
``` bash
docker compose up -d
```