package main

import (
	"fmt"
	"net/http"

	httpAdapter "github.com/SneatX/adeptos_go/internal/adapters/http"
	"github.com/SneatX/adeptos_go/internal/config"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
)

func main() {
	env := config.LoadEnv()
	container := config.BuildContainer(env)

	r := chi.NewRouter()
	r.Use(middleware.Logger)
	httpAdapter.RegisterRoutes(r, container)
	fmt.Println("Server started on port " + env.ServerConfig.Port)
	http.ListenAndServe(":"+env.ServerConfig.Port, r)
}
