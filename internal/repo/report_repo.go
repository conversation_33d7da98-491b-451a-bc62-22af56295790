package repo

import (
	"github.com/SneatX/adeptos_go/internal/domain"
	"gorm.io/gorm"
)

type ReportRepo struct {
	DB *gorm.DB
}

func NewReportRepo(db *gorm.DB) *ReportRepo {
	return &ReportRepo{DB: db}
}

func (r *ReportRepo) Create(report *domain.Report) (*domain.Report, error) {
	result := r.DB.Create(report)
	if result.Error != nil {
		return nil, result.Error
	}
	return report, nil
}

func (r *ReportRepo) GetByID(id uint) (*domain.Report, error) {
	var report domain.Report
	result := r.DB.First(&report, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &report, nil
}

func (r *ReportRepo) GetByLeadID(leadID uint) ([]*domain.Report, error) {
	var reports []*domain.Report
	result := r.DB.Where("lead_id = ?", leadID).Find(&reports)
	if result.Error != nil {
		return nil, result.Error
	}
	return reports, nil
}

func (r *ReportRepo) Update(report *domain.Report) (*domain.Report, error) {
	result := r.DB.Save(report)
	if result.Error != nil {
		return nil, result.Error
	}
	return report, nil
}

func (r *ReportRepo) Delete(id uint) error {
	result := r.DB.Delete(&domain.Report{}, id)
	return result.Error
}
