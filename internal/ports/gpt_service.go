package ports

import "github.com/SneatX/adeptos_go/internal/domain"

type GPTService interface {
	GenerateResponse(input LeadInput) (*domain.Report, error)
}

type LeadInput struct {
	FullName           string
	CompanyName        string
	Email              string
	Phone              string
	Website            *string
	Industry           string
	EmployeeCount      string
	BusinessType       string
	MonthlyRevenue     string
	SalesLocation      string
	TimeConsumingTasks string
	Bottlenecks        string
}
