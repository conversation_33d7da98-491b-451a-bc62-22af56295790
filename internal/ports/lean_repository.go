package ports

import "github.com/SneatX/adeptos_go/internal/domain"

type LeanRepository interface {
	Create(lead *domain.Lead) (*domain.Lead, error)
}

type ReportRepository interface {
	Create(report *domain.Report) (*domain.Report, error)
	GetByID(id uint) (*domain.Report, error)
	GetByLeadID(leadID uint) ([]*domain.Report, error)
	Update(report *domain.Report) (*domain.Report, error)
	Delete(id uint) error
}
