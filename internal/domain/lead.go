package domain

import "time"

type Lead struct {
	ID                 uint `gorm:"primaryKey"`
	FullName           string
	CompanyName        string
	Email              string
	Phone              string
	Website            *string
	Industry           string
	EmployeeCount      string
	BusinessType       string
	MonthlyRevenue     string
	SalesLocation      string
	TimeConsumingTasks string
	Bottlenecks        string
	Reports            []Report `gorm:"foreignKey:LeadID" json:"reports,omitempty"`
	CreatedAt          time.Time
}
