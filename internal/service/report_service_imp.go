package service

import (
	"time"

	"github.com/SneatX/adeptos_go/internal/domain"
	"github.com/SneatX/adeptos_go/internal/ports"
	"gorm.io/datatypes"
)

type ReportService struct {
	reportRepo ports.ReportRepository
}

func NewReportService(reportRepo ports.ReportRepository) *ReportService {
	return &ReportService{
		reportRepo: reportRepo,
	}
}

func (s *ReportService) CreateReport(leadID uint, data datatypes.JSON) (*domain.Report, error) {
	report := &domain.Report{
		LeadID:    leadID,
		Data:      data,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return s.reportRepo.Create(report)
}

func (s *ReportService) GetReportByID(id uint) (*domain.Report, error) {
	return s.reportRepo.GetByID(id)
}

func (s *ReportService) GetReportsByLeadID(leadID uint) ([]*domain.Report, error) {
	return s.reportRepo.GetByLeadID(leadID)
}

func (s *ReportService) UpdateReport(report *domain.Report) (*domain.Report, error) {
	report.UpdatedAt = time.Now()
	return s.reportRepo.Update(report)
}

func (s *ReportService) DeleteReport(id uint) error {
	return s.reportRepo.Delete(id)
}
