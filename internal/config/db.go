package config

import (
	"fmt"
	"log"

	"github.com/SneatX/adeptos_go/internal/domain"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func InitDatabase(cfg *DBConfig) *gorm.DB {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable", cfg.Host, cfg.User, cfg.Password, cfg.DBName, cfg.Port)

	DB, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Error in the db")
		return nil
	}

	DB.AutoMigrate(&domain.Lead{}, &domain.Report{})

	return DB
}
