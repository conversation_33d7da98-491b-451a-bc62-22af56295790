package responses

import (
	"encoding/json"
	"net/http"
)

type Response struct {
	Data  any     `json:"data"`
	Error *string `json:"error"`
}

func SuccessOne(w http.ResponseWriter, status int, data any) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(status)

	resp := Response{
		Data: data,
	}

	if err := json.NewEncoder(w).Encode(resp); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

func SuccessMany(w http.ResponseWriter, status int, data any) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	resp := Response{
		Data: data,
	}

	if err := json.NewEncoder(w).Encode(resp); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

func ErrorResponse(w http.ResponseWriter, status int, errMsg string) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	resp := Response{
		Error: &errMsg,
	}

	if err := json.NewEncoder(w).Encode(resp); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}
