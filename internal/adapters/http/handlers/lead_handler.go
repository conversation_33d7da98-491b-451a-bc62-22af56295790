package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/SneatX/adeptos_go/internal/adapters/http/responses"
	"github.com/SneatX/adeptos_go/internal/domain"
	"github.com/SneatX/adeptos_go/internal/ports"
)

type LeadHandler struct {
	leadService ports.LeadService
}

func NewLeadHandler(leadService ports.LeadService) *LeadHandler {
	return &LeadHandler{leadService: leadService}
}

func (h *LeadHandler) CreateLead(w http.ResponseWriter, r *http.Request) {
	var lead domain.Lead

	if err := json.NewDecoder(r.Body).Decode(&lead); err != nil {
		responses.ErrorResponse(w, http.StatusBadRequest, "Invalid JSON")
		return
	}

	if lead.FullName == "" || lead.Email == "" {
		responses.ErrorResponse(w, http.StatusBadRequest, "Missing required fields")
		return
	}

	res, err := h.leadService.CreateLeadAndGenerateResponse(&lead)
	if err != nil {
		responses.ErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}
	responses.SuccessOne(w, http.StatusCreated, res)
}
